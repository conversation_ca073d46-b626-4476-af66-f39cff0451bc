import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { User } from '../../models/user.model';
import { Appointment } from '../../models/appointment.model';

export interface NurseSelectionData {
  appointment: Appointment;
  availableNurses: User[];
}

@Component({
  selector: 'app-nurse-selection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatListModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <!-- Header moderne -->
    <div class="modal-header">
      <div class="header-content">
        <div class="header-icon">
          <mat-icon>person_add</mat-icon>
        </div>
        <div class="header-text">
          <h2 class="modal-title">Sélectionner un infirmier</h2>
          <p class="modal-subtitle">Choisissez un infirmier pour effectuer ce prélèvement</p>
        </div>
      </div>
      <button mat-icon-button (click)="onCancel()" class="close-btn">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <mat-dialog-content class="modal-content">
      <!-- Informations du rendez-vous -->
      <div class="appointment-summary">
        <div class="summary-header">
          <mat-icon>assignment</mat-icon>
          <h3>Détails du rendez-vous</h3>
        </div>

        <div class="summary-grid">
          <div class="summary-item">
            <div class="item-icon">
              <mat-icon>person</mat-icon>
            </div>
            <div class="item-content">
              <span class="item-label">Patient</span>
              <span class="item-value">{{ data.appointment.patient.firstName || 'N/A' }} {{ data.appointment.patient.lastName || 'N/A' }}</span>
            </div>
          </div>

          <div class="summary-item">
            <div class="item-icon">
              <mat-icon>schedule</mat-icon>
            </div>
            <div class="item-content">
              <span class="item-label">Date et heure</span>
              <span class="item-value">{{ data.appointment.scheduledDate | date:'dd/MM/yyyy HH:mm' }}</span>
            </div>
          </div>

          <div class="summary-item">
            <div class="item-icon">
              <mat-icon>location_on</mat-icon>
            </div>
            <div class="item-content">
              <span class="item-label">Adresse</span>
              <span class="item-value">{{ data.appointment.homeAddress }}</span>
            </div>
          </div>

          <div class="summary-item" *ngIf="data.appointment.analysisTypes && data.appointment.analysisTypes.length > 0">
            <div class="item-icon">
              <mat-icon>biotech</mat-icon>
            </div>
            <div class="item-content">
              <span class="item-label">Analyses ({{ data.appointment.analysisTypes.length }})</span>
              <div class="analyses-chips">
                <mat-chip-set>
                  <mat-chip *ngFor="let analysis of data.appointment.analysisTypes" class="analysis-chip">
                    {{ analysis.name }}
                  </mat-chip>
                </mat-chip-set>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Section des infirmiers -->
      <div class="nurses-section">
        <div class="section-header">
          <mat-icon>local_hospital</mat-icon>
          <h3>Infirmiers disponibles</h3>
          <span class="nurses-count">
            {{ data.availableNurses.length }}
            <span *ngIf="data.availableNurses.length > 1; else singular">disponibles</span>
            <ng-template #singular>disponible</ng-template>
          </span>
        </div>

        <!-- Liste des infirmiers -->
        <div class="nurses-grid" *ngIf="data.availableNurses.length > 0">
          <div
            *ngFor="let nurse of data.availableNurses; trackBy: trackByNurse"
            class="nurse-card"
            [class.selected]="selectedNurse?.id === nurse.id"
            (click)="selectNurse(nurse)">

            <div class="nurse-header">
              <div class="nurse-avatar">
                {{ nurse.firstName.charAt(0) }}{{ nurse.lastName.charAt(0) }}
              </div>
              <div class="nurse-info">
                <h4 class="nurse-name">{{ nurse.firstName }} {{ nurse.lastName }}</h4>
                <p class="nurse-email">{{ nurse.email }}</p>
              </div>
              <div class="selection-indicator" *ngIf="selectedNurse?.id === nurse.id">
                <mat-icon>check_circle</mat-icon>
              </div>
            </div>

            <div class="nurse-details">
              <div class="detail-row" *ngIf="nurse.phone">
                <mat-icon class="detail-icon">phone</mat-icon>
                <span>{{ nurse.phone }}</span>
              </div>

              <div class="detail-row" *ngIf="nurse.address">
                <mat-icon class="detail-icon">location_on</mat-icon>
                <span>{{ nurse.address }}</span>
              </div>

              <div class="detail-row">
                <mat-icon class="detail-icon">verified</mat-icon>
                <span class="status-available">Disponible maintenant</span>
              </div>
            </div>
          </div>
        </div>

        <!-- État vide -->
        <div *ngIf="data.availableNurses.length === 0" class="empty-state">
          <div class="empty-icon">
            <mat-icon>person_off</mat-icon>
          </div>
          <h4>Aucun infirmier disponible</h4>
          <p>Aucun infirmier disponible actuellement pour ce créneau.</p>
        </div>
      </div>
    </mat-dialog-content>

    <!-- Actions -->
    <div class="modal-actions">
      <button mat-stroked-button (click)="onCancel()" class="btn-cancel">
        <mat-icon>close</mat-icon>
        Annuler
      </button>

      <button
        mat-raised-button
        color="primary"
        (click)="onAssignSelected()"
        [disabled]="!selectedNurse"
        class="btn-assign">
        <mat-icon>check</mat-icon>
        <span *ngIf="selectedNurse; else defaultText">Affecter {{ selectedNurse.firstName }}</span>
        <ng-template #defaultText>Affecter un infirmier</ng-template>
      </button>
    </div>
  `,
  styles: [`
    // ========== MODAL STRUCTURE ==========

    :host {
      display: block;
    }

    ::ng-deep .mat-mdc-dialog-container {
      border-radius: 20px !important;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    }

    ::ng-deep .mat-mdc-dialog-content {
      padding: 0 !important;
      margin: 0 !important;
      max-height: none !important;
    }

    // ========== HEADER ==========

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      color: white;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
    }

    .header-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    .header-text {
      flex: 1;
    }

    .modal-title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      line-height: 1.2;
    }

    .modal-subtitle {
      margin: 2px 0 0 0;
      font-size: 0.85rem;
      opacity: 0.9;
      line-height: 1.3;
    }

    .close-btn {
      color: white;
      opacity: 0.8;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 1;
      }
    }

    // ========== CONTENT ==========

    .modal-content {
      padding: 24px;
      max-height: 60vh;
      overflow-y: auto;
    }

    // ========== APPOINTMENT SUMMARY ==========

    .appointment-summary {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 24px;
      border: 1px solid #e2e8f0;
    }

    .summary-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      mat-icon {
        color: #1976d2;
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .summary-grid {
      display: grid;
      gap: 8px;
    }

    .summary-item {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      padding: 12px;
      background: white;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      transition: all 0.2s ease;

      &:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .item-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      mat-icon {
        color: #1976d2;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }

    .item-content {
      flex: 1;
      min-width: 0;
    }

    .item-label {
      display: block;
      font-size: 0.8rem;
      font-weight: 500;
      color: #64748b;
      margin-bottom: 2px;
    }

    .item-value {
      display: block;
      font-size: 0.9rem;
      font-weight: 500;
      color: #1e293b;
      line-height: 1.3;
    }

    .analyses-chips {
      margin-top: 6px;
    }

    .analysis-chip {
      background: #e3f2fd !important;
      color: #1976d2 !important;
      font-size: 0.7rem !important;
      font-weight: 500 !important;
      margin: 1px !important;
      height: 24px !important;
    }

    // ========== NURSES SECTION ==========

    .nurses-section {
      margin-bottom: 24px;
    }

    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;

      mat-icon {
        color: #059669;
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        flex: 1;
      }

      .nurses-count {
        background: #059669;
        color: white;
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 500;
      }
    }

    // ========== NURSES GRID ==========

    .nurses-grid {
      display: grid;
      gap: 12px;
      max-height: 300px;
      overflow-y: auto;
      padding-right: 6px;

      /* Custom scrollbar */
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;

        &:hover {
          background: #94a3b8;
        }
      }
    }

    .nurse-card {
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      &.selected {
        border-color: #059669;
        background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
        box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
        transform: translateY(-1px);
      }
    }

    .nurse-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
      position: relative;
    }

    .nurse-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(135deg, #1976d2, #1565c0);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.1rem;
      text-transform: uppercase;
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
    }

    .nurse-info {
      flex: 1;
      min-width: 0;
    }

    .nurse-name {
      margin: 0 0 2px 0;
      font-size: 1rem;
      font-weight: 600;
      color: #1e293b;
      line-height: 1.3;
    }

    .nurse-email {
      margin: 0;
      font-size: 0.8rem;
      color: #64748b;
      line-height: 1.3;
    }

    .selection-indicator {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #059669;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(5, 150, 105, 0.4);

      mat-icon {
        color: white;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }

    .nurse-details {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .detail-row {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 0.8rem;
      color: #64748b;
    }

    .detail-icon {
      font-size: 14px !important;
      width: 14px !important;
      height: 14px !important;
      color: #94a3b8;
    }

    .status-available {
      color: #059669;
      font-weight: 500;
    }

    // ========== EMPTY STATE ==========

    .empty-state {
      text-align: center;
      padding: 48px 24px;
      background: #f8fafc;
      border-radius: 16px;
      border: 2px dashed #cbd5e1;
    }

    .empty-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;

      mat-icon {
        font-size: 40px;
        width: 40px;
        height: 40px;
        color: #f59e0b;
      }
    }

    .empty-state h4 {
      margin: 0 0 8px 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1e293b;
    }

    .empty-state p {
      margin: 0;
      font-size: 0.875rem;
      color: #64748b;
      line-height: 1.5;
    }

    // ========== MODAL ACTIONS ==========

    .modal-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      padding: 16px 24px;
      background: #f8fafc;
      border-top: 1px solid #e2e8f0;
    }

    .btn-cancel {
      min-width: 90px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 0.9rem;
      padding: 8px 16px;
    }



    .btn-assign {
      min-width: 130px;
      max-width: 180px;
      border-radius: 6px;
      font-weight: 600;
      font-size: 0.9rem;
      padding: 8px 16px;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:hover:not(:disabled) {
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
      }

      &:disabled {
        background: #e2e8f0;
        color: #94a3b8;
        box-shadow: none;
      }
    }

    // ========== RESPONSIVE DESIGN ==========

    @media (max-width: 768px) {
      .modal-header {
        padding: 16px 20px;
      }

      .header-content {
        gap: 10px;
      }

      .header-icon {
        width: 36px;
        height: 36px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }

      .modal-title {
        font-size: 1.1rem;
      }

      .modal-subtitle {
        font-size: 0.8rem;
      }

      .modal-content {
        padding: 20px 16px;
      }

      .appointment-summary {
        padding: 14px;
        margin-bottom: 20px;
      }

      .summary-item {
        padding: 10px;
      }

      .item-icon {
        width: 28px;
        height: 28px;

        mat-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }

      .nurse-card {
        padding: 12px;
      }

      .nurse-header {
        gap: 10px;
        margin-bottom: 10px;
      }

      .nurse-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }

      .nurse-name {
        font-size: 0.95rem;
      }

      .modal-actions {
        flex-direction: column;
        padding: 16px 20px;
        gap: 6px;
      }

      .btn-cancel,
      .btn-assign {
        width: 100%;
        min-width: auto;
        font-size: 0.85rem;
      }
    }

    @media (max-width: 480px) {
      .modal-header {
        padding: 16px 20px;
      }

      .modal-content {
        padding: 20px 16px;
      }

      .appointment-summary {
        padding: 16px;
      }

      .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
      }

      .item-icon {
        align-self: flex-start;
      }

      .nurses-grid {
        max-height: 300px;
      }

      .nurse-card {
        padding: 12px;
      }

      .nurse-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .selection-indicator {
        position: static;
        align-self: flex-end;
        margin-top: -32px;
      }

      .modal-actions {
        padding: 16px 20px;
      }
    }
  `]
})
export class NurseSelectionDialogComponent implements OnInit {
  selectedNurse: User | null = null;

  constructor(
    public dialogRef: MatDialogRef<NurseSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NurseSelectionData
  ) {}

  ngOnInit(): void {
    // Trier les infirmiers par nom
    this.data.availableNurses.sort((a, b) =>
      `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
    );
  }

  trackByNurse(index: number, nurse: User): number {
    return nurse.id || index;
  }

  selectNurse(nurse: User): void {
    this.selectedNurse = this.selectedNurse?.id === nurse.id ? null : nurse;
  }

  onCancel(): void {
    this.dialogRef.close();
  }



  onAssignSelected(): void {
    if (this.selectedNurse) {
      this.dialogRef.close({
        action: 'manual-assign',
        nurse: this.selectedNurse
      });
    }
  }

  // Méthode de compatibilité pour l'ancienne interface
  onAssignWithSelection(nurseList: any): void {
    const selectedOptions = nurseList.selectedOptions.selected;
    if (selectedOptions.length > 0) {
      const selectedNurse = selectedOptions[0].value;
      this.dialogRef.close({
        action: 'manual-assign',
        nurse: selectedNurse
      });
    }
  }
}

import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AppointmentService } from '../../services/appointment.service';
import { DateUtilsService } from '../../services/date-utils.service';
import { AnalysisType, AppointmentCreate } from '../../models/appointment.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';

@Component({
  selector: 'app-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="new-appointment-container">
      <div class="header-section">
        <div class="header-content">
          <div class="header-icon">
            <span class="custom-icon large">🩺</span>
          </div>
          <div class="header-text">
            <h1>Nouveau Rendez-vous</h1>
            <p>Planifiez votre prélèvement médical à domicile en quelques clics</p>
          </div>
        </div>
      </div>

      <mat-card class="appointment-card">
        <mat-card-content>
          <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()">

            <!-- Section 1: Informations de base -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">⏰</span>
                Quand souhaitez-vous votre rendez-vous ?
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Date et heure souhaitées</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-hint>Sélectionnez une date et heure qui vous conviennent</mat-hint>
                <mat-error *ngIf="appointmentForm.get('scheduledDate')?.hasError('required')">
                  La date est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 2: Localisation -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📍</span>
                Où doit avoir lieu le prélèvement ?
              </h3>

              <!-- Sélecteur de position simple -->
              <div class="location-section">
                <app-simple-location-picker
                  [initialLocation]="selectedMapLocation"
                  (locationSelected)="onLocationSelected($event)">
                </app-simple-location-picker>
              </div>

              <!-- Indicateur de statut de position -->
              <div class="position-status" *ngIf="!selectedMapLocation">
                <div class="status-warning">
                  <span class="custom-icon">⚠️</span>
                  <span>Position non sélectionnée - Utilisez la carte ci-dessus</span>
                </div>
              </div>

              <!-- Adresse confirmée -->
              <mat-form-field appearance="outline" class="full-width" *ngIf="selectedMapLocation">
                <mat-label>Adresse confirmée</mat-label>
                <textarea matInput formControlName="homeAddress" rows="3" required
                          [value]="selectedMapLocation.address || getCoordinatesString()"
                          readonly></textarea>
                <span class="custom-icon" matSuffix>✅</span>
                <mat-hint>Position sélectionnée sur la carte</mat-hint>
              </mat-form-field>

              <!-- Adresse manuelle si pas de position sélectionnée -->
              <mat-form-field appearance="outline" class="full-width" *ngIf="!selectedMapLocation">
                <mat-label>Ou saisissez l'adresse manuellement</mat-label>
                <textarea matInput formControlName="homeAddress" rows="3" required
                          placeholder="Numéro, rue, ville, code postal..."></textarea>
                <span class="custom-icon" matSuffix>✏️</span>
                <mat-hint>Vous pouvez aussi utiliser la carte ci-dessus</mat-hint>
                <mat-error *ngIf="appointmentForm.get('homeAddress')?.hasError('required')">
                  L'adresse est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 3: Types d'analyses -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">🧪</span>
                Quels types d'analyses souhaitez-vous ?
              </h3>

              <mat-form-field appearance="outline" class="full-width analysis-select">
                <mat-label>Sélectionnez les analyses</mat-label>
                <mat-select formControlName="analysisTypeIds" multiple required>
                  <mat-option *ngFor="let type of analysisTypes" [value]="type.id" class="analysis-option">
                    <div class="analysis-item">
                      <div class="analysis-main">
                        <span class="analysis-name">{{ type.name }}</span>
                        <span class="analysis-price">{{ type.price }}€</span>
                      </div>
                      <div class="analysis-details">
                        <div class="analysis-duration">
                          <span class="custom-icon">⏱️</span>
                          <span>{{ type.durationMinutes }}min</span>
                        </div>
                        <div class="analysis-prep" *ngIf="type.preparationInstructions">
                          <span class="custom-icon">📋</span>
                          <span>{{ type.preparationInstructions }}</span>
                        </div>
                      </div>
                    </div>
                  </mat-option>
                </mat-select>
                <mat-hint>Vous pouvez sélectionner plusieurs analyses</mat-hint>
                <mat-error *ngIf="appointmentForm.get('analysisTypeIds')?.hasError('required')">
                  Veuillez sélectionner au moins une analyse
                </mat-error>
              </mat-form-field>

              <!-- Affichage du prix estimé -->
              <div class="price-section" *ngIf="estimatedPrice > 0">
                <div class="price-card">
                  <div class="price-header">
                    <span class="custom-icon">💰</span>
                    <span>Prix estimé</span>
                  </div>
                  <div class="price-amount">{{ estimatedPrice }}€</div>
                  <div class="price-note">Prix indicatif, peut varier selon les conditions</div>
                </div>
              </div>
            </div>

            <!-- Section 4: Informations complémentaires -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📝</span>
                Informations complémentaires (optionnel)
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Symptômes ou raisons de l'analyse</mat-label>
                <textarea matInput formControlName="symptoms" rows="3"
                          placeholder="Décrivez brièvement vos symptômes ou la raison de cette analyse..."></textarea>
                <mat-hint>Ces informations aideront l'infirmier à mieux vous prendre en charge</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Instructions spéciales</mat-label>
                <textarea matInput formControlName="specialInstructions" rows="2"
                          placeholder="Allergies, difficultés d'accès, préférences particulières..."></textarea>
                <mat-hint>Mentionnez tout ce qui pourrait être utile à l'infirmier</mat-hint>
              </mat-form-field>

              <!-- Option urgence -->
              <div class="options-container">
                <mat-checkbox formControlName="isUrgent" class="urgent-checkbox">
                  <div class="checkbox-content">
                    <mat-icon>priority_high</mat-icon>
                    <div class="checkbox-text">
                      <span class="checkbox-title">Rendez-vous urgent</span>
                      <span class="checkbox-subtitle">Cochez si vous avez besoin d'un rendez-vous en priorité</span>
                    </div>
                  </div>
                </mat-checkbox>
              </div>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-stroked-button type="button" routerLink="/dashboard/appointments" class="cancel-btn">
                <span class="custom-icon">⬅️</span>
                Retour
              </button>
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="appointmentForm.invalid || isLoading" class="submit-btn">
                <span class="custom-icon" *ngIf="!isLoading">✅</span>
                <span class="custom-icon spinning" *ngIf="isLoading">⏳</span>
                <span *ngIf="!isLoading">Confirmer le rendez-vous</span>
                <span *ngIf="isLoading">Création en cours...</span>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .new-appointment-container {
      padding: 20px;
      max-width: 900px;
      margin: 0 auto;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }

    .header-section {
      margin-bottom: 30px;
      text-align: center;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      background: white;
      padding: 30px;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .header-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .header-icon .custom-icon.large {
      font-size: 40px;
    }

    .header-text h1 {
      margin: 0;
      color: #2c3e50;
      font-size: 2.2rem;
      font-weight: 600;
    }

    .header-text p {
      margin: 8px 0 0 0;
      color: #7f8c8d;
      font-size: 1.1rem;
    }

    .appointment-card {
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      border: none;
      overflow: hidden;
    }

    .form-section {
      margin-bottom: 40px;
      padding: 20px;
      background: #fafbfc;
      border-radius: 12px;
      border-left: 4px solid #667eea;
    }

    .location-section {
      margin-bottom: 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .position-status {
      margin-top: 1rem;
    }

    .status-warning {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 0.5rem;
      color: #856404;
      font-size: 0.9rem;
    }

    .status-warning .custom-icon {
      font-size: 1.1rem;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 20px 0;
      color: #2c3e50;
      font-size: 1.3rem;
      font-weight: 600;
    }

    .section-title .custom-icon {
      color: #667eea;
      font-size: 24px;
      margin-right: 8px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-option {
      height: auto !important;
      padding: 12px 16px !important;
      line-height: normal !important;
    }

    .analysis-item {
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .analysis-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .analysis-price {
      font-weight: 600;
      color: #27ae60;
      background: #e8f5e8;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.9rem;
    }

    .analysis-details {
      display: flex;
      gap: 16px;
      font-size: 0.85rem;
      color: #7f8c8d;
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .analysis-duration .custom-icon,
    .analysis-prep .custom-icon {
      font-size: 16px;
      margin-right: 4px;
    }

    .analysis-prep {
      color: #e67e22;
    }

    .options-container {
      background: white;
      padding: 20px;
      border-radius: 12px;
      border: 2px solid #ecf0f1;
    }

    .urgent-checkbox {
      margin: 0;
      width: 100%;
    }

    .checkbox-content {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
    }

    .checkbox-content mat-icon {
      color: #e74c3c;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .checkbox-text {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .checkbox-title {
      font-weight: 500;
      color: #2c3e50;
    }

    .checkbox-subtitle {
      font-size: 0.9rem;
      color: #7f8c8d;
    }

    .price-section {
      margin: 30px 0;
    }

    .price-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px;
      border-radius: 16px;
      text-align: center;
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    .price-header {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .price-amount {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .price-note {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      gap: 16px;
      margin-top: 40px;
      padding: 20px 0;
    }

    .cancel-btn {
      flex: 1;
      max-width: 200px;
      height: 48px;
      border-radius: 24px;
      font-weight: 500;
    }

    .submit-btn {
      flex: 2;
      height: 48px;
      border-radius: 24px;
      font-weight: 600;
      font-size: 1.1rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    }

    .submit-btn:hover {
      box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .new-appointment-container {
        padding: 16px;
      }

      .header-content {
        flex-direction: column;
        text-align: center;
        padding: 20px;
      }

      .header-text h1 {
        font-size: 1.8rem;
      }

      .form-actions {
        flex-direction: column;
      }

      .cancel-btn,
      .submit-btn {
        max-width: none;
      }
    }

    .custom-icon {
      font-size: 1.2em;
    }
  `]
})
export class NewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  estimatedPrice = 0;
  isLoading = false;
  selectedMapLocation?: SimpleLocation;

  constructor(
    private fb: FormBuilder,
    private appointmentService: AppointmentService,
    private router: Router,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private dateUtils: DateUtilsService
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    this.loadAnalysisTypes();
  }

  private loadAnalysisTypes(): void {
    this.appointmentService.getActiveAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private calculateEstimatedPrice(selectedIds: number[]): void {
    this.estimatedPrice = this.analysisTypes
      .filter(type => selectedIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
    this.cdr.detectChanges();
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner votre position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;

      // Corriger le problème de fuseau horaire pour la date en utilisant le service DateUtils
      let scheduledDate = formValue.scheduledDate;
      if (scheduledDate instanceof Date) {
        scheduledDate = this.dateUtils.formatDateForBackend(scheduledDate);
      }

      const appointmentData: AppointmentCreate = {
        scheduledDate: scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.appointmentService.createAppointment(appointmentData).subscribe({
        next: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/appointments']);
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onLocationSelected(location: SimpleLocation) {
    this.selectedMapLocation = location;

    // Mettre à jour le formulaire avec l'adresse sélectionnée
    const addressText = location.address || this.getCoordinatesString();
    this.appointmentForm.patchValue({
      homeAddress: addressText
    });

    this.cdr.detectChanges();

    this.snackBar.open('Position sélectionnée avec succès', 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  getCoordinatesString(): string {
    if (!this.selectedMapLocation) return '';
    return `${this.selectedMapLocation.latitude.toFixed(6)}, ${this.selectedMapLocation.longitude.toFixed(6)}`;
  }
}

import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AppointmentService } from '../../services/appointment.service';
import { DateUtilsService } from '../../services/date-utils.service';
import { AnalysisType, AppointmentCreate } from '../../models/appointment.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';

@Component({
  selector: 'app-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="new-appointment-container">
      <!-- Header moderne avec gradient -->
      <div class="modern-header">
        <div class="header-background"></div>
        <div class="header-content">
          <div class="header-icon-wrapper">
            <div class="header-icon">
              <span class="icon">🩺</span>
            </div>
          </div>
          <div class="header-text">
            <h1 class="main-title">Nouveau Rendez-vous</h1>
            <p class="subtitle">Planifiez votre prélèvement médical à domicile en toute simplicité</p>
          </div>
          <div class="header-actions">
            <button mat-stroked-button routerLink="/dashboard/appointments" class="back-button">
              <mat-icon>arrow_back</mat-icon>
              Retour
            </button>
          </div>
        </div>
      </div>

      <!-- Indicateur de progression -->
      <div class="progress-container">
        <div class="progress-steps">
          <div class="step" [class.active]="currentStep >= 1" [class.completed]="appointmentForm.get('scheduledDate')?.valid">
            <div class="step-circle">
              <span *ngIf="!appointmentForm.get('scheduledDate')?.valid">1</span>
              <mat-icon *ngIf="appointmentForm.get('scheduledDate')?.valid">check</mat-icon>
            </div>
            <span class="step-label">Date & Heure</span>
          </div>
          <div class="step-connector"></div>
          <div class="step" [class.active]="currentStep >= 2" [class.completed]="selectedMapLocation">
            <div class="step-circle">
              <span *ngIf="!selectedMapLocation">2</span>
              <mat-icon *ngIf="selectedMapLocation">check</mat-icon>
            </div>
            <span class="step-label">Localisation</span>
          </div>
          <div class="step-connector"></div>
          <div class="step" [class.active]="currentStep >= 3" [class.completed]="appointmentForm.get('analysisTypeIds')?.valid">
            <div class="step-circle">
              <span *ngIf="!appointmentForm.get('analysisTypeIds')?.valid">3</span>
              <mat-icon *ngIf="appointmentForm.get('analysisTypeIds')?.valid">check</mat-icon>
            </div>
            <span class="step-label">Analyses</span>
          </div>
          <div class="step-connector"></div>
          <div class="step" [class.active]="currentStep >= 4">
            <div class="step-circle">4</div>
            <span class="step-label">Confirmation</span>
          </div>
        </div>
      </div>

      <!-- Formulaire principal -->
      <div class="form-wrapper">
        <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="modern-form">

          <!-- Section 1: Date et Heure -->
          <div class="form-card" [class.completed]="appointmentForm.get('scheduledDate')?.valid">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="card-title">
                <h3>Date et Heure du Rendez-vous</h3>
                <p>Choisissez le moment qui vous convient le mieux</p>
              </div>
              <div class="card-status" *ngIf="appointmentForm.get('scheduledDate')?.valid">
                <mat-icon class="success-icon">check_circle</mat-icon>
              </div>
            </div>
            <div class="card-content">
              <mat-form-field appearance="outline" class="modern-field">
                <mat-label>Date et heure souhaitées</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-hint>
                  <mat-icon class="hint-icon">info</mat-icon>
                  Sélectionnez une date et heure qui vous conviennent
                </mat-hint>
                <mat-error *ngIf="appointmentForm.get('scheduledDate')?.hasError('required')">
                  <mat-icon class="error-icon">error</mat-icon>
                  La date est requise
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Section 2: Localisation -->
          <div class="form-card" [class.completed]="selectedMapLocation">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>location_on</mat-icon>
              </div>
              <div class="card-title">
                <h3>Localisation du Prélèvement</h3>
                <p>Indiquez où doit avoir lieu le prélèvement</p>
              </div>
              <div class="card-status" *ngIf="selectedMapLocation">
                <mat-icon class="success-icon">check_circle</mat-icon>
              </div>
            </div>
            <div class="card-content">
              <!-- Statut de localisation -->
              <div class="location-status" *ngIf="selectedMapLocation">
                <div class="status-success">
                  <mat-icon>location_on</mat-icon>
                  <span>Position sélectionnée avec succès</span>
                </div>
                <div class="selected-address">
                  <strong>Adresse :</strong> {{ selectedMapLocation.address || getCoordinatesString() }}
                </div>
              </div>

              <!-- Instructions pour la carte -->
              <div class="map-instructions" *ngIf="!selectedMapLocation">
                <mat-icon class="instruction-icon">touch_app</mat-icon>
                <span>Cliquez sur la carte pour sélectionner l'emplacement exact</span>
              </div>

              <!-- Sélecteur de position sur carte -->
              <div class="location-picker">
                <app-simple-location-picker
                  [initialLocation]="selectedMapLocation"
                  (locationSelected)="onLocationSelected($event)">
                </app-simple-location-picker>
              </div>

              <!-- Adresse manuelle alternative -->
              <div class="manual-address" *ngIf="!selectedMapLocation">
                <div class="divider">
                  <span class="divider-text">OU</span>
                </div>
                <mat-form-field appearance="outline" class="modern-field">
                  <mat-label>Saisissez votre adresse manuellement</mat-label>
                  <textarea matInput formControlName="homeAddress" rows="3" required
                            placeholder="Numéro, rue, ville, code postal..."></textarea>
                  <mat-icon matSuffix>edit_location</mat-icon>
                  <mat-hint>
                    <mat-icon class="hint-icon">info</mat-icon>
                    Utilisez de préférence la carte ci-dessus pour une localisation précise
                  </mat-hint>
                  <mat-error *ngIf="appointmentForm.get('homeAddress')?.hasError('required')">
                    <mat-icon class="error-icon">error</mat-icon>
                    L'adresse est requise
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>

          <!-- Section 3: Types d'analyses -->
          <div class="form-card" [class.completed]="appointmentForm.get('analysisTypeIds')?.valid">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>biotech</mat-icon>
              </div>
              <div class="card-title">
                <h3>Types d'Analyses</h3>
                <p>Sélectionnez les analyses que vous souhaitez effectuer</p>
              </div>
              <div class="card-status" *ngIf="appointmentForm.get('analysisTypeIds')?.valid">
                <mat-icon class="success-icon">check_circle</mat-icon>
              </div>
            </div>
            <div class="card-content">
              <mat-form-field appearance="outline" class="modern-field analysis-select">
                <mat-label>Sélectionnez les analyses</mat-label>
                <mat-select formControlName="analysisTypeIds" multiple required>
                  <mat-option *ngFor="let type of analysisTypes" [value]="type.id" class="analysis-option">
                    <div class="analysis-item">
                      <div class="analysis-main">
                        <span class="analysis-name">{{ type.name }}</span>
                        <span class="analysis-price">{{ type.price }}€</span>
                      </div>
                      <div class="analysis-details">
                        <div class="analysis-duration">
                          <mat-icon class="detail-icon">schedule</mat-icon>
                          <span>{{ type.durationMinutes }}min</span>
                        </div>
                        <div class="analysis-prep" *ngIf="type.preparationInstructions">
                          <mat-icon class="detail-icon">assignment</mat-icon>
                          <span>{{ type.preparationInstructions }}</span>
                        </div>
                      </div>
                    </div>
                  </mat-option>
                </mat-select>
                <mat-hint>
                  <mat-icon class="hint-icon">info</mat-icon>
                  Vous pouvez sélectionner plusieurs analyses
                </mat-hint>
                <mat-error *ngIf="appointmentForm.get('analysisTypeIds')?.hasError('required')">
                  <mat-icon class="error-icon">error</mat-icon>
                  Veuillez sélectionner au moins une analyse
                </mat-error>
              </mat-form-field>

              <!-- Affichage du prix estimé -->
              <div class="price-summary" *ngIf="estimatedPrice > 0">
                <div class="price-card">
                  <div class="price-header">
                    <mat-icon>euro</mat-icon>
                    <span>Prix estimé</span>
                  </div>
                  <div class="price-amount">{{ estimatedPrice }}€</div>
                  <div class="price-note">Prix indicatif, peut varier selon les conditions</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Section 4: Informations complémentaires -->
          <div class="form-card">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>description</mat-icon>
              </div>
              <div class="card-title">
                <h3>Informations Complémentaires</h3>
                <p>Ajoutez des détails pour optimiser votre prise en charge (optionnel)</p>
              </div>
            </div>
            <div class="card-content">
              <mat-form-field appearance="outline" class="modern-field">
                <mat-label>Symptômes ou raisons de l'analyse</mat-label>
                <textarea matInput formControlName="symptoms" rows="3"
                          placeholder="Décrivez brièvement vos symptômes ou la raison de cette analyse..."></textarea>
                <mat-hint>
                  <mat-icon class="hint-icon">info</mat-icon>
                  Ces informations aideront l'infirmier à mieux vous prendre en charge
                </mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline" class="modern-field">
                <mat-label>Instructions spéciales</mat-label>
                <textarea matInput formControlName="specialInstructions" rows="2"
                          placeholder="Allergies, difficultés d'accès, préférences particulières..."></textarea>
                <mat-hint>
                  <mat-icon class="hint-icon">info</mat-icon>
                  Mentionnez tout ce qui pourrait être utile à l'infirmier
                </mat-hint>
              </mat-form-field>

              <!-- Option urgence -->
              <div class="urgency-option">
                <mat-checkbox formControlName="isUrgent" class="urgency-checkbox">
                  <div class="checkbox-content">
                    <mat-icon class="urgency-icon">priority_high</mat-icon>
                    <div class="checkbox-text">
                      <span class="checkbox-title">Rendez-vous urgent</span>
                      <span class="checkbox-subtitle">Cochez si vous avez besoin d'un rendez-vous en priorité</span>
                    </div>
                  </div>
                </mat-checkbox>
              </div>
            </div>
          </div>

          <!-- Actions finales -->
          <div class="form-actions">
            <button mat-stroked-button type="button" routerLink="/dashboard/appointments" class="action-btn secondary">
              <mat-icon>arrow_back</mat-icon>
              Retour
            </button>
            <button mat-raised-button color="primary" type="submit"
                    [disabled]="appointmentForm.invalid || isLoading" class="action-btn primary">
              <mat-icon *ngIf="!isLoading">check</mat-icon>
              <mat-icon *ngIf="isLoading" class="spinning">hourglass_empty</mat-icon>
              <span *ngIf="!isLoading">Confirmer le rendez-vous</span>
              <span *ngIf="isLoading">Création en cours...</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  `,
  styles: [`
    // ========== VARIABLES ET BASE ==========

    :host {
      display: block;
      --primary-color: #667eea;
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --background-light: #f9fafb;
      --border-color: #e5e7eb;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }

    .new-appointment-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      padding: 2rem 1rem;
    }

    // ========== HEADER MODERNE ==========

    .modern-header {
      position: relative;
      margin-bottom: 3rem;
      overflow: hidden;
      border-radius: 24px;
      box-shadow: var(--shadow-xl);
    }

    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--primary-gradient);
      opacity: 0.95;
    }

    .header-content {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2.5rem;
      color: white;
      z-index: 1;
    }

    .header-icon-wrapper {
      display: flex;
      align-items: center;
    }

    .header-icon {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .header-icon .icon {
      font-size: 2.5rem;
    }

    .header-text {
      flex: 1;
      margin-left: 2rem;
    }

    .main-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      background: linear-gradient(45deg, #ffffff, #f1f5f9);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      font-size: 1.125rem;
      margin: 0;
      opacity: 0.9;
      font-weight: 400;
    }

    .back-button {
      background: rgba(255, 255, 255, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .back-button:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-2px);
    }

    // ========== INDICATEUR DE PROGRESSION ==========

    .progress-container {
      max-width: 800px;
      margin: 0 auto 3rem auto;
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: var(--shadow-md);
    }

    .progress-steps {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
      z-index: 2;
      position: relative;
    }

    .step-circle {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: var(--border-color);
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 3px solid transparent;
    }

    .step.active .step-circle {
      background: var(--primary-color);
      color: white;
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
    }

    .step.completed .step-circle {
      background: var(--success-color);
      color: white;
    }

    .step-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-secondary);
      text-align: center;
    }

    .step.active .step-label,
    .step.completed .step-label {
      color: var(--text-primary);
      font-weight: 600;
    }

    .step-connector {
      flex: 1;
      height: 3px;
      background: var(--border-color);
      margin: 0 1rem;
      border-radius: 2px;
    }

    // ========== FORMULAIRE MODERNE ==========

    .form-wrapper {
      max-width: 900px;
      margin: 0 auto;
    }

    .modern-form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    // ========== CARTES DE FORMULAIRE ==========

    .form-card {
      background: white;
      border-radius: 20px;
      box-shadow: var(--shadow-md);
      overflow: hidden;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .form-card:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }

    .form-card.completed {
      border-color: var(--success-color);
      background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
    }

    .card-header {
      display: flex;
      align-items: center;
      padding: 2rem 2rem 1rem 2rem;
      border-bottom: 1px solid var(--border-color);
    }

    .card-icon {
      width: 60px;
      height: 60px;
      background: var(--primary-gradient);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      margin-right: 1.5rem;
    }

    .card-icon mat-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;
    }

    .card-title {
      flex: 1;
    }

    .card-title h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .card-title p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 1rem;
    }

    .card-status {
      display: flex;
      align-items: center;
    }

    .success-icon {
      color: var(--success-color);
      font-size: 32px !important;
      width: 32px !important;
      height: 32px !important;
    }

    .card-content {
      padding: 2rem;
    }

    // ========== CHAMPS DE FORMULAIRE MODERNES ==========

    .modern-field {
      width: 100%;
      margin-bottom: 1.5rem;
    }

    .modern-field .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.5rem;
    }

    .hint-icon,
    .error-icon {
      font-size: 16px !important;
      width: 16px !important;
      height: 16px !important;
      margin-right: 0.5rem;
      vertical-align: middle;
    }

    .error-icon {
      color: var(--error-color);
    }

    // ========== LOCALISATION SPÉCIFIQUE ==========

    .location-status {
      margin-bottom: 1.5rem;
    }

    .status-success {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 1.25rem;
      background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
      border: 1px solid #d1fae5;
      border-radius: 12px;
      color: var(--success-color);
      margin-bottom: 0.75rem;
    }

    .selected-address {
      font-size: 0.9rem;
      color: var(--text-secondary);
      padding-left: 2.5rem;
    }

    .map-instructions {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 1.25rem;
      background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
      border: 1px solid #bfdbfe;
      border-radius: 12px;
      color: #1e40af;
      margin-bottom: 1.5rem;
      font-size: 0.9rem;
    }

    .instruction-icon {
      font-size: 20px !important;
      width: 20px !important;
      height: 20px !important;
    }

    .location-picker {
      margin-bottom: 1.5rem;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: var(--shadow-md);
    }

    .manual-address {
      margin-top: 2rem;
    }

    .divider {
      text-align: center;
      margin: 1.5rem 0;
      position: relative;
    }

    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: var(--border-color);
    }

    .divider-text {
      background: white;
      padding: 0 1rem;
      color: var(--text-secondary);
      font-weight: 500;
      position: relative;
    }

    // ========== RESPONSIVE DESIGN ==========

    @media (max-width: 768px) {
      .new-appointment-container {
        padding: 1rem 0.5rem;
      }

      .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 2rem 1.5rem;
      }

      .header-text {
        margin-left: 0;
      }

      .main-title {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1rem;
      }

      .progress-container {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
      }

      .step-circle {
        width: 40px;
        height: 40px;
      }

      .step-label {
        font-size: 0.75rem;
      }

      .step-connector {
        margin: 0 0.5rem;
      }

      .card-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }

      .card-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
      }

      .card-content {
        padding: 1.5rem;
      }

      .card-title h3 {
        font-size: 1.25rem;
      }
    }

    @media (max-width: 480px) {
      .new-appointment-container {
        padding: 0.5rem;
      }

      .header-content {
        padding: 1.5rem 1rem;
      }

      .main-title {
        font-size: 1.75rem;
      }

      .progress-container {
        padding: 1rem;
      }

      .progress-steps {
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
      }

      .step-connector {
        display: none;
      }

      .card-header {
        padding: 1rem;
      }

      .card-content {
        padding: 1rem;
      }

      .card-icon {
        width: 50px;
        height: 50px;
      }

      .card-icon mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }

    // ========== ANIMATIONS ==========

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .form-card {
      animation: slideInUp 0.6s ease-out;
    }

    .form-card:nth-child(2) {
      animation-delay: 0.1s;
    }

    .form-card:nth-child(3) {
      animation-delay: 0.2s;
    }

    .form-card:nth-child(4) {
      animation-delay: 0.3s;
    }

    // ========== UTILITAIRES ==========

    .custom-icon {
      font-size: 1.2em;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    // ========== SECTIONS SPÉCIFIQUES ==========

    // Analyses
    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-option {
      height: auto !important;
      padding: 12px 16px !important;
      line-height: normal !important;
    }

    .analysis-item {
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .analysis-name {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 1rem;
    }

    .analysis-price {
      font-weight: 700;
      color: var(--success-color);
      background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 0.9rem;
      border: 1px solid #d1fae5;
    }

    .analysis-details {
      display: flex;
      gap: 1rem;
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .detail-icon {
      font-size: 16px !important;
      width: 16px !important;
      height: 16px !important;
    }

    .analysis-prep {
      color: var(--warning-color);
    }

    // Prix estimé
    .price-summary {
      margin-top: 2rem;
    }

    .price-card {
      background: var(--primary-gradient);
      color: white;
      padding: 2rem;
      border-radius: 20px;
      text-align: center;
      box-shadow: var(--shadow-lg);
      position: relative;
      overflow: hidden;
    }

    .price-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
      pointer-events: none;
    }

    .price-header {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      margin-bottom: 1rem;
      font-size: 1.125rem;
      opacity: 0.9;
      position: relative;
    }

    .price-amount {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
      position: relative;
    }

    .price-note {
      font-size: 0.875rem;
      opacity: 0.8;
      position: relative;
    }

    // Options d'urgence
    .urgency-option {
      background: linear-gradient(135deg, #fef3c7 0%, #fef7cd 100%);
      border: 2px solid #f59e0b;
      border-radius: 16px;
      padding: 1.5rem;
      margin-top: 1.5rem;
    }

    .urgency-checkbox {
      width: 100%;
    }

    .checkbox-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      width: 100%;
    }

    .urgency-icon {
      color: var(--warning-color);
      font-size: 28px !important;
      width: 28px !important;
      height: 28px !important;
    }

    .checkbox-text {
      flex: 1;
    }

    .checkbox-title {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 1.125rem;
      margin-bottom: 0.25rem;
    }

    .checkbox-subtitle {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    // Actions finales
    .form-actions {
      display: flex;
      gap: 1.5rem;
      margin-top: 3rem;
      padding: 2rem 0;
    }

    .action-btn {
      flex: 1;
      height: 56px;
      border-radius: 16px;
      font-weight: 600;
      font-size: 1.125rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      transition: all 0.3s ease;
    }

    .action-btn.secondary {
      background: white;
      border: 2px solid var(--border-color);
      color: var(--text-secondary);
    }

    .action-btn.secondary:hover {
      border-color: var(--primary-color);
      color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .action-btn.primary {
      background: var(--primary-gradient);
      box-shadow: var(--shadow-lg);
      color: white;
      border: none;
    }

    .action-btn.primary:hover:not(:disabled) {
      transform: translateY(-3px);
      box-shadow: var(--shadow-xl);
    }

    .action-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  `]
})
export class NewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  estimatedPrice = 0;
  isLoading = false;
  selectedMapLocation?: SimpleLocation;
  currentStep = 1;

  constructor(
    private fb: FormBuilder,
    private appointmentService: AppointmentService,
    private router: Router,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private dateUtils: DateUtilsService
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    this.loadAnalysisTypes();
  }

  private loadAnalysisTypes(): void {
    this.appointmentService.getActiveAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private calculateEstimatedPrice(selectedIds: number[]): void {
    this.estimatedPrice = this.analysisTypes
      .filter(type => selectedIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
    this.cdr.detectChanges();
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner votre position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;

      // Corriger le problème de fuseau horaire pour la date en utilisant le service DateUtils
      let scheduledDate = formValue.scheduledDate;
      if (scheduledDate instanceof Date) {
        scheduledDate = this.dateUtils.formatDateForBackend(scheduledDate);
      }

      const appointmentData: AppointmentCreate = {
        scheduledDate: scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.appointmentService.createAppointment(appointmentData).subscribe({
        next: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/appointments']);
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onLocationSelected(location: SimpleLocation) {
    this.selectedMapLocation = location;

    // Mettre à jour le formulaire avec l'adresse sélectionnée
    const addressText = location.address || this.getCoordinatesString();
    this.appointmentForm.patchValue({
      homeAddress: addressText
    });

    this.cdr.detectChanges();

    this.snackBar.open('Position sélectionnée avec succès', 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  getCoordinatesString(): string {
    if (!this.selectedMapLocation) return '';
    return `${this.selectedMapLocation.latitude.toFixed(6)}, ${this.selectedMapLocation.longitude.toFixed(6)}`;
  }
}

import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AppointmentService } from '../../services/appointment.service';
import { DateUtilsService } from '../../services/date-utils.service';
import { AnalysisType, AppointmentCreate } from '../../models/appointment.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';

@Component({
  selector: 'app-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="new-appointment-container">
      <div class="container">
        <!-- Header moderne -->
        <div class="dashboard-header">
          <div class="welcome-section">
            <h1 class="dashboard-title">
              <span class="custom-icon">🩺</span>
              Nouveau Rendez-vous
            </h1>
            <p class="dashboard-subtitle">
              Planifiez votre prélèvement médical à domicile en quelques clics
            </p>
          </div>
          <div class="quick-actions">
            <button mat-stroked-button routerLink="/dashboard" class="btn-secondary">
              <mat-icon>arrow_back</mat-icon>
              Retour au tableau de bord
            </button>
          </div>
        </div>

        <!-- Indicateur de progression -->
        <div class="progress-indicator" *ngIf="appointmentForm">
          <div class="progress-steps">
            <div class="step" [class.completed]="appointmentForm.get('scheduledDate')?.valid" [class.active]="!appointmentForm.get('scheduledDate')?.valid">
              <span class="step-number">1</span>
              <span class="step-label">Date</span>
            </div>
            <div class="step" [class.completed]="selectedMapLocation || appointmentForm.get('homeAddress')?.valid" [class.active]="appointmentForm.get('scheduledDate')?.valid && !selectedMapLocation && !appointmentForm.get('homeAddress')?.valid">
              <span class="step-number">2</span>
              <span class="step-label">Lieu</span>
            </div>
            <div class="step" [class.completed]="appointmentForm.get('analysisTypeIds')?.valid" [class.active]="(selectedMapLocation || appointmentForm.get('homeAddress')?.valid) && !appointmentForm.get('analysisTypeIds')?.valid">
              <span class="step-number">3</span>
              <span class="step-label">Analyses</span>
            </div>
            <div class="step" [class.active]="appointmentForm.get('analysisTypeIds')?.valid">
              <span class="step-number">4</span>
              <span class="step-label">Finaliser</span>
            </div>
          </div>
        </div>

        <!-- Formulaire principal -->
        <div class="form-container" *ngIf="appointmentForm">
          <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="appointment-form">

            <!-- Section 1: Date et heure -->
            <div class="form-section" [class.completed]="appointmentForm.get('scheduledDate')?.valid">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">⏰</span>
                  Quand souhaitez-vous votre rendez-vous ?
                  <span class="completion-badge" *ngIf="appointmentForm.get('scheduledDate')?.valid">✓</span>
                </h2>
              </div>
              <div class="section-content">
                <div class="field-group">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Date et heure souhaitées</mat-label>
                    <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-hint>
                      <mat-icon class="hint-icon">info</mat-icon>
                      Sélectionnez une date et heure qui vous conviennent
                    </mat-hint>
                    <mat-error *ngIf="appointmentForm.get('scheduledDate')?.hasError('required')">
                      <mat-icon>error</mat-icon>
                      La date est requise
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>
            </div>

            <!-- Section 2: Localisation -->
            <div class="form-section" [class.completed]="selectedMapLocation">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">📍</span>
                  Où doit avoir lieu le prélèvement ?
                  <span class="completion-badge" *ngIf="selectedMapLocation">✓</span>
                </h2>
              </div>
              <div class="section-content">
                <!-- Indicateur de statut de localisation -->
                <div class="location-status" *ngIf="selectedMapLocation">
                  <div class="status-indicator success">
                    <mat-icon>location_on</mat-icon>
                    <span>Position sélectionnée sur la carte</span>
                  </div>
                  <div class="selected-address">
                    <strong>Adresse :</strong> {{ selectedMapLocation.address || getCoordinatesString() }}
                  </div>
                </div>

                <!-- Sélecteur de position sur carte -->
                <div class="location-picker-container">
                  <div class="map-instructions" *ngIf="!selectedMapLocation">
                    <mat-icon class="instruction-icon">touch_app</mat-icon>
                    <span>Cliquez sur la carte pour sélectionner l'emplacement exact du prélèvement</span>
                  </div>
                  <app-simple-location-picker
                    [initialLocation]="selectedMapLocation"
                    (locationSelected)="onLocationSelected($event)">
                  </app-simple-location-picker>
                </div>

                <!-- Adresse manuelle si pas de position sélectionnée -->
                <div class="manual-address-section" *ngIf="!selectedMapLocation">
                  <div class="divider">
                    <span class="divider-text">OU</span>
                  </div>
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Saisissez votre adresse manuellement</mat-label>
                    <textarea matInput formControlName="homeAddress" rows="3" required
                              placeholder="Numéro, rue, ville, code postal..."></textarea>
                    <mat-icon matSuffix>edit_location</mat-icon>
                    <mat-hint>
                      <mat-icon class="hint-icon">info</mat-icon>
                      Utilisez de préférence la carte ci-dessus pour une localisation précise
                    </mat-hint>
                    <mat-error *ngIf="appointmentForm.get('homeAddress')?.hasError('required')">
                      <mat-icon>error</mat-icon>
                      L'adresse est requise
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>
            </div>

            <!-- Section 3: Types d'analyses -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">🧪</span>
                Quelles analyses souhaitez-vous ?
              </h3>

              <mat-form-field appearance="outline" class="full-width analysis-select">
                <mat-label>Sélectionnez vos analyses</mat-label>
                <mat-select formControlName="analysisTypeIds" multiple required>
                  <mat-option *ngFor="let analysis of analysisTypes" [value]="analysis.id" class="analysis-option">
                    <div class="analysis-item">
                      <div class="analysis-main">
                        <span class="analysis-name">{{analysis.name}}</span>
                        <span class="analysis-price">{{analysis.price}}€</span>
                      </div>
                      <div class="analysis-details">
                        <span class="analysis-duration">
                          <span class="custom-icon">⏱️</span>
                          {{analysis.durationMinutes}} min
                        </span>
                        <span class="analysis-prep" *ngIf="analysis.preparationRequired">
                          <span class="custom-icon">⚠️</span>
                          Préparation requise
                        </span>
                      </div>
                    </div>
                  </mat-option>
                </mat-select>
                <mat-hint>Vous pouvez sélectionner plusieurs analyses</mat-hint>
                <mat-error *ngIf="appointmentForm.get('analysisTypeIds')?.hasError('required')">
                  Sélectionnez au moins une analyse
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 4: Informations complémentaires -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📝</span>
                Informations complémentaires
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Symptômes (optionnel)</mat-label>
                <textarea matInput formControlName="symptoms" rows="3"
                          placeholder="Décrivez vos symptômes pour nous aider à mieux vous conseiller..."></textarea>
                <span class="custom-icon" matSuffix>🩹</span>
                <mat-hint>Ces informations aideront notre équipe médicale</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Instructions spéciales (optionnel)</mat-label>
                <textarea matInput formControlName="specialInstructions" rows="2"
                          placeholder="Informations particulières : accès, préparation, allergies..."></textarea>
                <span class="custom-icon" matSuffix>ℹ️</span>
                <mat-hint>Toute information utile pour l'infirmière</mat-hint>
              </mat-form-field>
            </div>

            <!-- Section 5: Options -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">⚙️</span>
                Options du rendez-vous
              </h3>

              <div class="options-container">
                <mat-checkbox formControlName="isUrgent" class="urgent-checkbox">
                  <div class="checkbox-content">
                    <span class="custom-icon">🚨</span>
                    <div class="checkbox-text">
                      <span class="checkbox-title">Demande urgente</span>
                      <span class="checkbox-subtitle">Traitement prioritaire (supplément possible)</span>
                    </div>
                  </div>
                </mat-checkbox>
              </div>
            </div>

            <!-- Prix estimé -->
            <div class="price-section" *ngIf="estimatedPrice > 0">
              <div class="price-card">
                <div class="price-header">
                  <span class="custom-icon">💰</span>
                  <span>Estimation du coût</span>
                </div>
                <div class="price-amount">{{estimatedPrice}}€</div>
                <div class="price-note">Prix indicatif, peut varier selon les conditions</div>
              </div>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-stroked-button type="button" routerLink="/dashboard/appointments" class="cancel-btn">
                <span class="custom-icon">⬅️</span>
                Retour
              </button>
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="appointmentForm.invalid || isLoading" class="submit-btn">
                <span class="custom-icon" *ngIf="!isLoading">✅</span>
                <span class="custom-icon spinning" *ngIf="isLoading">⏳</span>
                <span *ngIf="!isLoading">Confirmer le rendez-vous</span>
                <span *ngIf="isLoading">Création en cours...</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    // ========== STRUCTURE MODERNE COHÉRENTE ==========

    :host {
      display: block;
    }

    .new-appointment-container {
      min-height: calc(100vh - 80px);
      background-color: #f9fafb;
      padding: 1.5rem 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    // ========== HEADER MODERNE ==========

    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem;
      border-radius: 1rem;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    }

    .welcome-section {
      flex: 1;
    }

    .dashboard-title {
      font-size: 2rem;
      font-weight: 700;
      color: #1976d2;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .dashboard-subtitle {
      font-size: 1.125rem;
      color: #64748b;
      margin: 0;
    }

    .quick-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .btn-secondary {
      border-radius: 8px;
    }

    // ========== PROGRESS INDICATOR ==========

    .progress-indicator {
      background: white;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .progress-steps {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: #e2e8f0;
        z-index: 1;
      }
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      position: relative;
      z-index: 2;

      .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e2e8f0;
        color: #64748b;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .step-label {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
        text-align: center;
      }

      &.active {
        .step-number {
          background: #1976d2;
          color: white;
          box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
        }
        .step-label {
          color: #1976d2;
          font-weight: 600;
        }
      }

      &.completed {
        .step-number {
          background: #059669;
          color: white;
        }
        .step-label {
          color: #059669;
          font-weight: 600;
        }
      }
    }

    // ========== FORM CONTAINER ==========

    .form-container {
      background: white;
      border-radius: 16px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-section {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.completed {
        border-color: #059669;
        background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
      }
    }

    .section-header {
      margin-bottom: 24px;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .section-content {
      margin-top: 16px;
    }

    .completion-badge {
      background: #059669;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      margin-left: 8px;
    }

    .field-group {
      margin-bottom: 20px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .hint-icon {
      font-size: 16px !important;
      width: 16px !important;
      height: 16px !important;
      margin-right: 4px;
      vertical-align: middle;
    }

    // ========== LOCATION ENHANCEMENTS ==========

    .location-status {
      margin-bottom: 20px;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 8px;

      &.success {
        background: #ecfdf5;
        color: #059669;
        border: 1px solid #d1fae5;
      }
    }

    .selected-address {
      font-size: 0.9rem;
      color: #64748b;
      padding-left: 32px;
    }

    .map-instructions {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      border-radius: 8px;
      margin-bottom: 16px;
      color: #1e40af;
      font-size: 0.9rem;
    }

    .instruction-icon {
      font-size: 20px !important;
    }

    .location-picker-container {
      margin-bottom: 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .manual-address-section {
      margin-top: 24px;
    }

    .divider {
      text-align: center;
      margin: 20px 0;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e2e8f0;
      }

      .divider-text {
        background: white;
        padding: 0 16px;
        color: #64748b;
        font-weight: 500;
        position: relative;
      }
    }

    // ========== UTILITIES ==========

    .custom-icon {
      font-size: 1.2em;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    // ========== RESPONSIVE DESIGN ==========

    @media (max-width: 768px) {
      .new-appointment-container {
        padding: 1rem 0;
      }

      .container {
        padding: 0 16px;
      }

      .dashboard-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 1rem;
      }

      .dashboard-title {
        font-size: 1.5rem;
      }

      .form-container {
        padding: 20px;
        margin-bottom: 16px;
      }

      .form-section {
        padding: 16px;
        margin-bottom: 16px;
      }

      .section-title {
        font-size: 1.25rem;
      }

      .progress-indicator {
        padding: 16px;
      }

      .step {
        .step-number {
          width: 32px;
          height: 32px;
          font-size: 0.875rem;
        }

        .step-label {
          font-size: 0.75rem;
        }
      }

      .location-picker-container {
        margin-bottom: 16px;
      }
    }

    @media (max-width: 480px) {
      .dashboard-title {
        font-size: 1.25rem;
      }

      .form-container {
        padding: 16px;
      }

      .form-section {
        padding: 12px;
      }

      .section-title {
        font-size: 1.125rem;
      }

      .progress-steps {
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;

        &::before {
          display: none;
        }
      }

      .step {
        min-width: 80px;
      }
    }
  `]
})
export class NewAppointmentComponent implements OnInit {
    }

    .status-warning {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 0.5rem;
      color: #856404;
      font-size: 0.9rem;
    }

    .status-warning .custom-icon {
      font-size: 1.1rem;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 20px 0;
      color: #2c3e50;
      font-size: 1.3rem;
      font-weight: 600;
    }

    .section-title .custom-icon {
      color: #667eea;
      font-size: 24px;
      margin-right: 8px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-option {
      height: auto !important;
      padding: 12px 16px !important;
      line-height: normal !important;
    }

    .analysis-item {
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .analysis-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .analysis-price {
      font-weight: 600;
      color: #27ae60;
      background: #e8f5e8;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.9rem;
    }

    .analysis-details {
      display: flex;
      gap: 16px;
      font-size: 0.85rem;
      color: #7f8c8d;
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .analysis-duration .custom-icon,
    .analysis-prep .custom-icon {
      font-size: 16px;
      margin-right: 4px;
    }

    .analysis-prep {
      color: #e67e22;
    }

    .options-container {
      background: white;
      padding: 20px;
      border-radius: 12px;
      border: 2px solid #ecf0f1;
    }

    .urgent-checkbox {
      margin: 0;
      width: 100%;
    }

    .checkbox-content {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
    }

    .checkbox-content mat-icon {
      color: #e74c3c;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .checkbox-text {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .checkbox-title {
      font-weight: 500;
      color: #2c3e50;
    }

    .checkbox-subtitle {
      font-size: 0.9rem;
      color: #7f8c8d;
    }

    .price-section {
      margin: 30px 0;
    }

    .price-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px;
      border-radius: 16px;
      text-align: center;
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    .price-header {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .price-amount {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .price-note {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      gap: 16px;
      margin-top: 40px;
      padding: 20px 0;
    }

    .cancel-btn {
      flex: 1;
      max-width: 200px;
      height: 48px;
      border-radius: 24px;
      font-weight: 500;
    }

    .submit-btn {
      flex: 2;
      height: 48px;
      border-radius: 24px;
      font-weight: 600;
      font-size: 1.1rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    }

    .submit-btn:hover {
      box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .new-appointment-container {
        padding: 16px;
      }

      .header-content {
        flex-direction: column;
        text-align: center;
        padding: 20px;
      }

      .header-text h1 {
        font-size: 1.8rem;
      }

      .form-actions {
        flex-direction: column;
      }

      .cancel-btn,
      .submit-btn {
        max-width: none;
      }
    }
  `]
})
export class NewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  estimatedPrice = 0;
  isLoading = false;
  selectedMapLocation?: SimpleLocation;

  constructor(
    private fb: FormBuilder,
    private appointmentService: AppointmentService,
    private router: Router,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private dateUtils: DateUtilsService
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    this.loadAnalysisTypes();
  }

  private loadAnalysisTypes(): void {
    this.appointmentService.getActiveAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private calculateEstimatedPrice(selectedIds: number[]): void {
    this.estimatedPrice = this.analysisTypes
      .filter(type => selectedIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
    this.cdr.detectChanges();
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner votre position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;

      // Corriger le problème de fuseau horaire pour la date en utilisant le service DateUtils
      let scheduledDate = formValue.scheduledDate;
      if (scheduledDate instanceof Date) {
        scheduledDate = this.dateUtils.formatDateForBackend(scheduledDate);
      }

      const appointmentData: AppointmentCreate = {
        scheduledDate: scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.appointmentService.createAppointment(appointmentData).subscribe({
        next: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/appointments']);
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onLocationSelected(location: SimpleLocation) {
    this.selectedMapLocation = location;

    // Mettre à jour le formulaire avec l'adresse sélectionnée
    const addressText = location.address || this.getCoordinatesString();
    this.appointmentForm.patchValue({
      homeAddress: addressText
    });

    this.cdr.detectChanges();

    this.snackBar.open('Position sélectionnée avec succès', 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  getCoordinatesString(): string {
    if (!this.selectedMapLocation) return '';
    return `${this.selectedMapLocation.latitude.toFixed(6)}, ${this.selectedMapLocation.longitude.toFixed(6)}`;
  }
}

import { Component, OnInit, ChangeDetectorRef, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormsModule } from '@angular/forms';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AdminService } from '../../services/admin.service';
import { AppointmentService } from '../../services/appointment.service';
import { AnalysisType, AppointmentCreate, Appointment, AppointmentStatus } from '../../models/appointment.model';
import { User } from '../../models/user.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';
import { NurseSelectionDialogComponent, NurseSelectionData } from '../nurse-selection-dialog/nurse-selection-dialog.component';

// Composant modal de test simple
@Component({
  selector: 'app-test-modal',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule],
  template: `
    <h2 mat-dialog-title>Test Modal</h2>
    <mat-dialog-content>
      <p>{{ data.message }}</p>
      <p>Ce modal de test fonctionne !</p>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button (click)="close()">Fermer</button>
    </mat-dialog-actions>
  `
})
export class TestModalComponent {
  constructor(
    public dialogRef: MatDialogRef<TestModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  close(): void {
    this.dialogRef.close('test-result');
  }
}

@Component({
  selector: 'app-admin-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatDialogModule,
    MatListModule,
    MatChipsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="admin-new-appointment-container">
      <div class="container">
        <!-- Header moderne -->
        <div class="dashboard-header">
          <div class="welcome-section">
            <h1 class="dashboard-title">
              <span class="custom-icon">👨‍💼</span>
              Nouveau rendez-vous
            </h1>
            <p class="dashboard-subtitle">
              Créer un nouveau rendez-vous pour {{ selectedPatient?.firstName }} {{ selectedPatient?.lastName }}
            </p>
          </div>
          <div class="quick-actions">
            <button mat-stroked-button routerLink="/dashboard/admin-appointments" class="btn-secondary">
              <mat-icon>arrow_back</mat-icon>
              Retour
            </button>
          </div>
        </div>
        <!-- Carte du patient -->
        <div class="patient-info-section" *ngIf="selectedPatient">
          <div class="section-header">
            <h2 class="section-title">
              <span class="custom-icon">👤</span>
              Informations du patient
            </h2>
          </div>
          <div class="patient-card">
            <div class="patient-main-info">
              <div class="patient-avatar">
                {{ selectedPatient.firstName.charAt(0) }}{{ selectedPatient.lastName.charAt(0) }}
              </div>
              <div class="patient-details">
                <div class="patient-name">{{ selectedPatient.firstName }} {{ selectedPatient.lastName }}</div>
                <div class="patient-email">{{ selectedPatient.email }}</div>
                <div class="patient-phone" *ngIf="selectedPatient.phone">{{ selectedPatient.phone }}</div>
              </div>
            </div>
            <div class="patient-additional-info" *ngIf="selectedPatient.address">
              <div class="info-item">
                <span class="info-label">📍 Adresse :</span>
                <span class="info-value">{{ selectedPatient.address }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- Indicateur de progression -->
        <div class="progress-indicator" *ngIf="appointmentForm">
          <div class="progress-steps">
            <div class="step" [class.completed]="selectedPatient" [class.active]="selectedPatient">
              <span class="step-number">1</span>
              <span class="step-label">Patient</span>
            </div>
            <div class="step" [class.completed]="appointmentForm.get('scheduledDate')?.valid" [class.active]="!appointmentForm.get('scheduledDate')?.valid && selectedPatient">
              <span class="step-number">2</span>
              <span class="step-label">Date</span>
            </div>
            <div class="step" [class.completed]="selectedMapLocation" [class.active]="appointmentForm.get('scheduledDate')?.valid && !selectedMapLocation">
              <span class="step-number">3</span>
              <span class="step-label">Lieu</span>
            </div>
            <div class="step" [class.completed]="appointmentForm.get('analysisTypeIds')?.valid" [class.active]="selectedMapLocation && !appointmentForm.get('analysisTypeIds')?.valid">
              <span class="step-number">4</span>
              <span class="step-label">Analyses</span>
            </div>
            <div class="step" [class.active]="appointmentForm.get('analysisTypeIds')?.valid">
              <span class="step-number">5</span>
              <span class="step-label">Finaliser</span>
            </div>
          </div>
        </div>

        <!-- Formulaire principal -->
        <div class="form-container" *ngIf="appointmentForm">
          <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="appointment-form">

            <!-- Section 1: Informations de base -->
            <div class="form-section" [class.completed]="appointmentForm.get('scheduledDate')?.valid">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">⏰</span>
                  Date et heure du rendez-vous
                  <span class="completion-badge" *ngIf="appointmentForm.get('scheduledDate')?.valid">✓</span>
                </h2>
              </div>
              <div class="section-content">
                <div class="field-group">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Date et heure souhaitées</mat-label>
                    <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-hint>
                      <mat-icon class="hint-icon">info</mat-icon>
                      Sélectionnez une date et heure pour le patient
                    </mat-hint>
                    <mat-error *ngIf="appointmentForm && appointmentForm.get('scheduledDate')?.hasError('required')">
                      <mat-icon>error</mat-icon>
                      La date est requise
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>
            </div>

            <!-- Section 2: Localisation -->
            <div class="form-section" [class.completed]="selectedMapLocation">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">📍</span>
                  Où se déroule le rendez-vous ?
                  <span class="completion-badge" *ngIf="selectedMapLocation">✓</span>
                </h2>
              </div>
              <div class="section-content">
                <!-- Indicateur de statut de localisation -->
                <div class="location-status" *ngIf="selectedMapLocation">
                  <div class="status-indicator success">
                    <mat-icon>location_on</mat-icon>
                    <span>Position sélectionnée sur la carte</span>
                  </div>
                  <div class="selected-address">
                    <strong>Adresse :</strong> {{ selectedMapLocation.address }}
                  </div>
                </div>

                <!-- Sélecteur de position sur carte -->
                <div class="location-picker-container">
                  <div class="map-instructions" *ngIf="!selectedMapLocation">
                    <mat-icon class="instruction-icon">touch_app</mat-icon>
                    <span>Cliquez sur la carte pour sélectionner l'emplacement exact du rendez-vous</span>
                  </div>
                  <app-simple-location-picker
                    (locationSelected)="onLocationSelected($event)"
                    [initialLocation]="selectedMapLocation">
                  </app-simple-location-picker>
                </div>

                <!-- Adresse manuelle si pas de position sélectionnée -->
                <div class="manual-address-section" *ngIf="!selectedMapLocation">
                  <div class="divider">
                    <span class="divider-text">OU</span>
                  </div>
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Saisissez l'adresse manuellement</mat-label>
                    <textarea matInput formControlName="homeAddress" rows="3" required
                              placeholder="Numéro, rue, ville, code postal..."></textarea>
                    <mat-icon matSuffix>edit_location</mat-icon>
                    <mat-hint>
                      <mat-icon class="hint-icon">info</mat-icon>
                      Utilisez de préférence la carte ci-dessus pour une localisation précise
                    </mat-hint>
                    <mat-error *ngIf="appointmentForm && appointmentForm.get('homeAddress')?.hasError('required')">
                      <mat-icon>error</mat-icon>
                      L'adresse est requise
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>
            </div>

            <!-- Section 3: Types d'analyses -->
            <div class="form-section" [class.completed]="appointmentForm.get('analysisTypeIds')?.valid">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">🧪</span>
                  Analyses demandées
                  <span class="completion-badge" *ngIf="appointmentForm.get('analysisTypeIds')?.valid">✓</span>
                </h2>
              </div>
              <div class="section-content">
                <!-- Sélection des analyses -->
                <div class="field-group">
                  <mat-form-field appearance="outline" class="full-width analysis-select">
                    <mat-label>Sélectionnez les analyses</mat-label>
                    <mat-select formControlName="analysisTypeIds" multiple required>
                      <mat-option *ngFor="let analysis of analysisTypes" [value]="analysis.id" class="analysis-option">
                        <div class="analysis-item">
                          <div class="analysis-main">
                            <span class="analysis-name">{{analysis.name}}</span>
                            <span class="analysis-price">{{analysis.price}}€</span>
                          </div>
                          <div class="analysis-details">
                            <span class="analysis-duration">
                              <mat-icon class="detail-icon">schedule</mat-icon>
                              {{analysis.durationMinutes}} min
                            </span>
                            <span class="analysis-prep" *ngIf="analysis.preparationRequired">
                              <mat-icon class="detail-icon warning">warning</mat-icon>
                              Préparation requise
                            </span>
                          </div>
                        </div>
                      </mat-option>
                    </mat-select>
                    <mat-hint>
                      <mat-icon class="hint-icon">info</mat-icon>
                      Vous pouvez sélectionner plusieurs analyses
                    </mat-hint>
                    <mat-error *ngIf="appointmentForm && appointmentForm.get('analysisTypeIds')?.hasError('required')">
                      <mat-icon>error</mat-icon>
                      Au moins une analyse est requise
                    </mat-error>
                  </mat-form-field>
                </div>

                <!-- Résumé des analyses sélectionnées -->
                <div class="selected-analyses" *ngIf="appointmentForm.get('analysisTypeIds')?.value?.length > 0">
                  <h4 class="summary-title">
                    <mat-icon>assignment</mat-icon>
                    Analyses sélectionnées ({{ appointmentForm.get('analysisTypeIds')?.value?.length }})
                  </h4>
                  <div class="analyses-chips">
                    <mat-chip-set>
                      <mat-chip *ngFor="let analysisId of appointmentForm.get('analysisTypeIds')?.value"
                               class="analysis-chip">
                        {{ getAnalysisName(analysisId) }}
                        <span class="chip-price">{{ getAnalysisPrice(analysisId) }}€</span>
                      </mat-chip>
                    </mat-chip-set>
                  </div>
                </div>

                <!-- Prix estimé -->
                <div class="price-summary" *ngIf="estimatedPrice > 0">
                  <div class="price-header">
                    <mat-icon>euro</mat-icon>
                    <span class="price-label">Prix total estimé</span>
                  </div>
                  <div class="price-details">
                    <span class="price-value">{{ estimatedPrice }}€</span>
                    <span class="price-note">TTC</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Section 4: Informations complémentaires -->
            <div class="form-section">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">📝</span>
                  Informations complémentaires
                </h2>
              </div>
              <div class="section-content">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Symptômes (optionnel)</mat-label>
                  <textarea matInput formControlName="symptoms" rows="3"
                            placeholder="Décrivez les symptômes du patient..."></textarea>
                  <span class="custom-icon" matSuffix>🩹</span>
                  <mat-hint>Ces informations aideront notre équipe médicale</mat-hint>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Instructions spéciales (optionnel)</mat-label>
                  <textarea matInput formControlName="specialInstructions" rows="3"
                            placeholder="Instructions particulières pour l'infirmier..."></textarea>
                  <span class="custom-icon" matSuffix>📋</span>
                  <mat-hint>Informations importantes pour le prélèvement</mat-hint>
                </mat-form-field>

                <div class="checkbox-section">
                  <mat-checkbox formControlName="isUrgent" class="urgent-checkbox">
                    <span class="custom-icon">🚨</span>
                    Rendez-vous urgent
                  </mat-checkbox>
                  <p class="checkbox-hint">
                    Cochez cette case si le rendez-vous nécessite une prise en charge prioritaire
                  </p>
                </div>
              </div>
            </div>

            <!-- Section 5: Affectation d'infirmier -->
            <div class="form-section">
              <div class="section-header">
                <h2 class="section-title">
                  <span class="custom-icon">👩‍⚕️</span>
                  Affectation d'infirmier (optionnel)
                </h2>
              </div>
              <div class="section-content">
                <div class="nurse-assignment-options">
                  <div class="assignment-choice">
                    <input type="radio" id="no-assignment" name="nurseAssignment" value="none"
                           [checked]="nurseAssignmentChoice === 'none'"
                           (change)="onNurseAssignmentChoiceChange('none')" class="radio-input">
                    <label for="no-assignment" class="radio-label">
                      <span class="custom-icon">⏳</span>
                      Assigner plus tard
                    </label>
                    <p class="choice-description">Le rendez-vous sera créé en attente d'affectation</p>
                  </div>

                  <div class="assignment-choice">
                    <input type="radio" id="auto-assignment" name="nurseAssignment" value="auto"
                           [checked]="nurseAssignmentChoice === 'auto'"
                           (change)="onNurseAssignmentChoiceChange('auto')" class="radio-input">
                    <label for="auto-assignment" class="radio-label">
                      <span class="custom-icon">🤖</span>
                      Affectation automatique
                    </label>
                    <p class="choice-description">L'infirmier le plus proche sera automatiquement assigné</p>
                  </div>

                  <div class="assignment-choice">
                    <input type="radio" id="manual-assignment" name="nurseAssignment" value="manual"
                           [checked]="nurseAssignmentChoice === 'manual'"
                           (change)="onNurseAssignmentChoiceChange('manual')" class="radio-input">
                    <label for="manual-assignment" class="radio-label">
                      <span class="custom-icon">👥</span>
                      Choisir un infirmier
                    </label>
                    <p class="choice-description">Sélectionner manuellement un infirmier disponible</p>
                  </div>
                </div>

                <!-- Bouton pour ouvrir le modal de sélection -->
                <div class="nurse-selection-button" *ngIf="nurseAssignmentChoice === 'manual'">
                  <button type="button"
                          class="select-nurse-btn"
                          (click)="openNurseSelectionModal()"
                          [disabled]="!availableNurses.length">
                    <span class="custom-icon">👩‍⚕️</span>
                    {{ selectedNurse ? 'Changer d\'infirmier' : 'Sélectionner un infirmier' }}
                    <span class="btn-count" *ngIf="availableNurses.length">({{ availableNurses.length }} disponibles)</span>
                  </button>

                  <!-- Résumé de l'infirmier sélectionné -->
                  <div class="selected-nurse-summary" *ngIf="selectedNurse">
                    <div class="summary-header">
                      <span class="custom-icon">✅</span>
                      Infirmier sélectionné
                    </div>
                    <div class="summary-content">
                      <div class="nurse-avatar-small">
                        {{ selectedNurse.firstName.charAt(0) }}{{ selectedNurse.lastName.charAt(0) }}
                      </div>
                      <div class="nurse-details">
                        <strong>{{ selectedNurse.firstName }} {{ selectedNurse.lastName }}</strong>
                        <span class="summary-email">{{ selectedNurse.email }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-stroked-button type="button" routerLink="/dashboard/admin-appointments" class="btn-secondary">
                <mat-icon>arrow_back</mat-icon>
                Retour
              </button>
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="!appointmentForm || appointmentForm.invalid || isLoading" class="btn-primary">
                <mat-icon *ngIf="!isLoading">check</mat-icon>
                <mat-icon *ngIf="isLoading" class="spinning">hourglass_empty</mat-icon>
                <span *ngIf="!isLoading">Créer le rendez-vous</span>
                <span *ngIf="isLoading">Création en cours...</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    // ========== STRUCTURE MODERNE COHÉRENTE ==========

    :host {
      display: block;
    }

    .admin-new-appointment-container {
      min-height: calc(100vh - 80px);
      background-color: #f9fafb;
      padding: 1.5rem 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    // ========== HEADER MODERNE ==========

    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem;
      border-radius: 1rem;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    }

    .welcome-section {
      flex: 1;
    }

    .dashboard-title {
      font-size: 2rem;
      font-weight: 700;
      color: #1976d2;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .dashboard-subtitle {
      font-size: 1.125rem;
      color: #64748b;
      margin: 0;
    }

    .quick-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .btn-secondary {
      border-radius: 8px;
    }

    // ========== SECTIONS MODERNES ==========

    .patient-info-section,
    .form-container {
      background: white;
      border-radius: 16px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      margin-bottom: 24px;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    // ========== PATIENT CARD ==========

    .patient-card {
      background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
      border-radius: 12px;
      padding: 24px;
      border-left: 4px solid #2196f3;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .patient-main-info {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
    }

    .patient-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #2196f3, #21cbf3);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.2rem;
      text-transform: uppercase;
    }

    .patient-details {
      flex: 1;
    }

    .patient-name {
      font-size: 1.3rem;
      font-weight: 600;
      color: #1976d2;
      margin-bottom: 4px;
    }

    .patient-email {
      color: #666;
      font-size: 0.95rem;
      margin-bottom: 2px;
    }

    .patient-phone {
      color: #666;
      font-size: 0.9rem;
    }

    .patient-additional-info {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
    }

    .info-label {
      font-weight: 500;
      color: #555;
    }

    .info-value {
      color: #333;
    }

    // ========== FORM SECTIONS ==========

    .form-section {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .section-content {
      margin-top: 16px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .location-picker-container {
      margin-bottom: 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    // ========== ANALYSIS SELECTION ==========

    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-item {
      padding: 12px 0;
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
    }

    .analysis-name {
      font-weight: 500;
      color: #1e293b;
    }

    .analysis-price {
      font-weight: 600;
      color: #059669;
      background: #ecfdf5;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 0.875rem;
    }

    .analysis-details {
      display: flex;
      gap: 16px;
      font-size: 0.85rem;
      color: #64748b;
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .price-summary {
      background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
      border: 2px solid #059669;
      padding: 20px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 20px;
      box-shadow: 0 4px 12px rgba(5, 150, 105, 0.1);
    }

    .price-label {
      font-weight: 500;
      color: #1e293b;
    }

    .price-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: #059669;
    }

    // ========== FORM ELEMENTS ==========

    .checkbox-section {
      margin-top: 20px;
      padding: 16px;
      background: #fef2f2;
      border-radius: 8px;
      border-left: 4px solid #ef4444;
    }

    .urgent-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-hint {
      margin: 8px 0 0 32px;
      font-size: 0.9rem;
      color: #64748b;
    }

    // ========== NURSE ASSIGNMENT ==========

    .nurse-assignment-options {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
    }

    .assignment-choice {
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 20px;
      transition: all 0.3s ease;
      cursor: pointer;
      background: white;

      &:hover {
        border-color: #1976d2;
        background: #f8fafc;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:has(.radio-input:checked) {
        border-color: #1976d2;
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
      }
    }

    .radio-input {
      margin-right: 12px;
      transform: scale(1.3);
      accent-color: #1976d2;
    }

    .radio-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #1e293b;
      cursor: pointer;
      margin-bottom: 8px;
      font-size: 1.1rem;
    }

    .choice-description {
      margin: 0;
      color: #64748b;
      font-size: 0.9rem;
      margin-left: 32px;
      line-height: 1.5;
    }

    .nurse-selection-button {
      margin-top: 24px;
    }

    .select-nurse-btn {
      width: 100%;
      padding: 16px 24px;
      background: linear-gradient(135deg, #1976d2, #1565c0);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 16px;
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #1565c0, #0d47a1);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
      }

      &:disabled {
        background: #e2e8f0;
        color: #94a3b8;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }

    .btn-count {
      font-size: 0.9rem;
      opacity: 0.9;
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: 6px;
    }

    .selected-nurse-summary {
      padding: 20px;
      background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
      border-radius: 12px;
      border: 2px solid #059669;
      box-shadow: 0 4px 12px rgba(5, 150, 105, 0.1);
    }

    .summary-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #059669;
      margin-bottom: 16px;
      font-size: 1.1rem;
    }

    .summary-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .nurse-avatar-small {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #059669, #10b981);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1rem;
      text-transform: uppercase;
    }

    .nurse-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .summary-email {
      font-size: 0.9rem;
      color: #64748b;
    }

    // ========== FORM ACTIONS ==========

    .form-actions {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      padding: 24px 0;
      border-top: 2px solid #e2e8f0;
      margin-top: 32px;
    }

    .btn-secondary {
      min-width: 140px;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
    }

    .btn-primary {
      min-width: 200px;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);

      &:hover {
        box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
      }
    }

    // ========== PROGRESS INDICATOR ==========

    .progress-indicator {
      background: white;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .progress-steps {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: #e2e8f0;
        z-index: 1;
      }
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      position: relative;
      z-index: 2;

      .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e2e8f0;
        color: #64748b;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .step-label {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
        text-align: center;
      }

      &.active {
        .step-number {
          background: #1976d2;
          color: white;
          box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
        }
        .step-label {
          color: #1976d2;
          font-weight: 600;
        }
      }

      &.completed {
        .step-number {
          background: #059669;
          color: white;
        }
        .step-label {
          color: #059669;
          font-weight: 600;
        }
      }
    }

    // ========== FORM ENHANCEMENTS ==========

    .form-section {
      &.completed {
        border-color: #059669;
        background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
      }
    }

    .completion-badge {
      background: #059669;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      margin-left: 8px;
    }

    .field-group {
      margin-bottom: 20px;
    }

    .hint-icon,
    .detail-icon {
      font-size: 16px !important;
      width: 16px !important;
      height: 16px !important;
      margin-right: 4px;
      vertical-align: middle;
    }

    .detail-icon.warning {
      color: #f59e0b;
    }

    // ========== LOCATION ENHANCEMENTS ==========

    .location-status {
      margin-bottom: 20px;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 8px;

      &.success {
        background: #ecfdf5;
        color: #059669;
        border: 1px solid #d1fae5;
      }
    }

    .selected-address {
      font-size: 0.9rem;
      color: #64748b;
      padding-left: 32px;
    }

    .map-instructions {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      border-radius: 8px;
      margin-bottom: 16px;
      color: #1e40af;
      font-size: 0.9rem;
    }

    .instruction-icon {
      font-size: 20px !important;
    }

    .manual-address-section {
      margin-top: 24px;
    }

    .divider {
      text-align: center;
      margin: 20px 0;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e2e8f0;
      }

      .divider-text {
        background: white;
        padding: 0 16px;
        color: #64748b;
        font-weight: 500;
        position: relative;
      }
    }

    // ========== ANALYSIS ENHANCEMENTS ==========

    .selected-analyses {
      margin-top: 24px;
      padding: 20px;
      background: #f8fafc;
      border-radius: 12px;
      border: 1px solid #e2e8f0;
    }

    .summary-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e293b;
    }

    .analyses-chips {
      margin-bottom: 16px;
    }

    .analysis-chip {
      background: #e3f2fd !important;
      color: #1976d2 !important;
      margin: 4px !important;
      padding: 8px 12px !important;
      border-radius: 8px !important;
      font-weight: 500 !important;
    }

    .chip-price {
      background: #1976d2;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.75rem;
      margin-left: 8px;
    }

    .price-summary {
      .price-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
      }

      .price-details {
        display: flex;
        align-items: baseline;
        gap: 8px;
      }

      .price-note {
        font-size: 0.875rem;
        color: #64748b;
      }
    }

    // ========== UTILITIES ==========

    .custom-icon {
      font-size: 1.2em;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    // ========== RESPONSIVE DESIGN ==========

    @media (max-width: 768px) {
      .admin-new-appointment-container {
        padding: 1rem 0;
      }

      .container {
        padding: 0 16px;
      }

      .dashboard-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 1rem;
      }

      .dashboard-title {
        font-size: 1.5rem;
      }

      .patient-info-section,
      .form-container {
        padding: 20px;
        margin-bottom: 16px;
      }

      .form-section {
        padding: 16px;
        margin-bottom: 16px;
      }

      .section-title {
        font-size: 1.25rem;
      }

      .form-actions {
        flex-direction: column;
        gap: 12px;
      }

      .btn-secondary,
      .btn-primary {
        width: 100%;
        min-width: auto;
      }

      .patient-main-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .patient-additional-info {
        flex-direction: column;
        gap: 8px;
      }

      .assignment-choice {
        padding: 16px;
      }

      .radio-label {
        font-size: 1rem;
      }

      .select-nurse-btn {
        padding: 14px 20px;
      }

      .price-summary {
        padding: 16px;
      }

      .price-value {
        font-size: 1.25rem;
      }

      .analysis-item {
        padding: 8px 0;
      }

      .location-picker-container {
        margin-bottom: 16px;
      }
    }

    @media (max-width: 480px) {
      .dashboard-title {
        font-size: 1.25rem;
      }

      .patient-info-section,
      .form-container {
        padding: 16px;
      }

      .form-section {
        padding: 12px;
      }

      .section-title {
        font-size: 1.125rem;
      }

      .patient-avatar {
        width: 50px;
        height: 50px;
        font-size: 1rem;
      }

      .nurse-avatar-small {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
      }
    }
  `]
})
export class AdminNewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  selectedPatient: User | null = null;
  patientId: number | null = null;
  isLoading = false;
  estimatedPrice = 0;
  selectedMapLocation: SimpleLocation | undefined = undefined;

  // Propriétés pour l'affectation d'infirmier
  nurseAssignmentChoice: 'none' | 'auto' | 'manual' = 'none';
  availableNurses: User[] = [];
  selectedNurse: User | null = null;

  constructor(
    private fb: FormBuilder,
    private adminService: AdminService,
    private appointmentService: AppointmentService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    // Récupérer l'ID du patient depuis les paramètres de route
    this.route.params.subscribe(params => {
      this.patientId = +params['patientId'];
      if (this.patientId) {
        this.loadPatientInfo();
      }
    });

    this.loadAnalysisTypes();
    this.loadAvailableNurses();
  }

  loadPatientInfo(): void {
    if (!this.patientId) return;

    this.adminService.getPatientById(this.patientId).subscribe({
      next: (patient) => {
        this.selectedPatient = patient;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement patient:', error);
        this.snackBar.open('Erreur lors du chargement des informations du patient', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/dashboard/admin']);
      }
    });
  }

  loadAnalysisTypes(): void {
    this.appointmentService.getAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types.filter(type => type.isActive);
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  calculateEstimatedPrice(analysisTypeIds: number[]): void {
    if (!analysisTypeIds || analysisTypeIds.length === 0) {
      this.estimatedPrice = 0;
      return;
    }

    this.estimatedPrice = this.analysisTypes
      .filter(type => analysisTypeIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
  }

  onLocationSelected(location: SimpleLocation): void {
    this.selectedMapLocation = location;
    if (location) {
      this.appointmentForm.patchValue({
        homeAddress: location.address
      });
    }
  }

  onSubmit(): void {
    if (this.appointmentForm && this.appointmentForm.valid && this.patientId) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner la position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;
      const appointmentData: AppointmentCreate = {
        scheduledDate: formValue.scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.adminService.createAppointmentForPatient(this.patientId, appointmentData).subscribe({
        next: (appointment) => {
          this.isLoading = false;
          this.cdr.detectChanges();

          // Gérer l'affectation d'infirmier si nécessaire
          if (this.nurseAssignmentChoice !== 'none') {
            this.handleNurseAssignment(appointment.id);
          }

          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/admin-appointments'], {
            queryParams: { view: 'patients' }
          });
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  testSimpleModal(): void {
    console.log('🧪 TEST - Ouverture d\'un modal simple');
    try {
      const dialogRef = this.dialog.open(TestModalComponent, {
        width: '400px',
        data: { message: 'Test modal simple' }
      });

      console.log('✅ Modal simple ouvert:', dialogRef);

      dialogRef.afterClosed().subscribe(result => {
        console.log('Modal simple fermé:', result);
      });
    } catch (error) {
      console.error('❌ Erreur modal simple:', error);
    }
  }

  loadAvailableNurses(): void {
    this.adminService.getAvailableNurses().subscribe({
      next: (nurses) => {
        this.availableNurses = nurses;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement infirmiers:', error);
        this.snackBar.open('Erreur lors du chargement des infirmiers', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  openNurseSelectionModal(): void {
    try {
      console.log('🔍 DÉBUT - Ouverture du modal de sélection d\'infirmier');
      console.log('Infirmiers disponibles:', this.availableNurses);
      console.log('Patient sélectionné:', this.selectedPatient);
      console.log('Dialog service:', this.dialog);

      // Vérifications préalables
      if (!this.selectedPatient) {
        console.error('❌ Aucun patient sélectionné');
        this.snackBar.open('Erreur: Aucun patient sélectionné', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
        return;
      }

      if (!this.availableNurses || this.availableNurses.length === 0) {
        console.error('❌ Aucun infirmier disponible');
        this.snackBar.open('Aucun infirmier disponible pour le moment', 'Fermer', {
          duration: 3000,
          panelClass: ['warning-snackbar']
        });
        return;
      }

      console.log('✅ Vérifications passées, création du rendez-vous temporaire...');

    // Créer un rendez-vous temporaire pour le modal
    const tempAppointment: Appointment = {
      id: 0, // ID temporaire
      patient: this.selectedPatient!,
      analysisTypes: this.analysisTypes.filter(type =>
        this.appointmentForm.get('analysisTypeIds')?.value?.includes(type.id)
      ),
      scheduledDate: this.appointmentForm.get('scheduledDate')?.value,
      homeAddress: this.appointmentForm.get('homeAddress')?.value,
      latitude: this.selectedMapLocation?.latitude,
      longitude: this.selectedMapLocation?.longitude,
      status: AppointmentStatus.PENDING,
      symptoms: this.appointmentForm.get('symptoms')?.value,
      specialInstructions: this.appointmentForm.get('specialInstructions')?.value,
      totalPrice: this.estimatedPrice,
      isUrgent: this.appointmentForm.get('isUrgent')?.value || false,
      estimatedDurationMinutes: this.analysisTypes
        .filter(type => this.appointmentForm.get('analysisTypeIds')?.value?.includes(type.id))
        .reduce((total, type) => total + type.durationMinutes, 0),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const dialogData: NurseSelectionData = {
      appointment: tempAppointment,
      availableNurses: this.availableNurses
    };

    console.log('📋 Données du modal:', dialogData);

    const dialogRef = this.dialog.open(NurseSelectionDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: false
    });

    console.log('🎭 Modal ouvert, référence:', dialogRef);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.action === 'auto-assign') {
          this.nurseAssignmentChoice = 'auto';
          this.selectedNurse = null;
        } else if (result.action === 'manual-assign' && result.nurse) {
          this.nurseAssignmentChoice = 'manual';
          this.selectedNurse = result.nurse;
        }
        this.cdr.detectChanges();
      }
    });

    } catch (error) {
      console.error('💥 ERREUR lors de l\'ouverture du modal:', error);
      this.snackBar.open('Erreur technique lors de l\'ouverture du modal', 'Fermer', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    }
  }

  onNurseAssignmentChoiceChange(choice: 'none' | 'auto' | 'manual'): void {
    this.nurseAssignmentChoice = choice;

    // Réinitialiser l'infirmier sélectionné si on change vers 'none' ou 'auto'
    if (choice !== 'manual') {
      this.selectedNurse = null;
    }

    this.cdr.detectChanges();
  }

  private handleNurseAssignment(appointmentId: number): void {
    if (this.nurseAssignmentChoice === 'auto') {
      // Affectation automatique
      this.adminService.autoAssignNearestNurse(appointmentId).subscribe({
        next: () => {
          this.snackBar.open('Infirmier assigné automatiquement!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur affectation automatique:', error);
          this.snackBar.open('Erreur lors de l\'affectation automatique', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else if (this.nurseAssignmentChoice === 'manual' && this.selectedNurse) {
      // Affectation manuelle
      this.adminService.assignNurseToAppointment(appointmentId, this.selectedNurse.id!).subscribe({
        next: () => {
          this.snackBar.open(`Infirmier ${this.selectedNurse!.firstName} ${this.selectedNurse!.lastName} assigné!`, 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur affectation manuelle:', error);
          this.snackBar.open('Erreur lors de l\'affectation de l\'infirmier', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  // Méthodes utilitaires pour l'affichage des analyses
  getAnalysisName(analysisId: number): string {
    const analysis = this.analysisTypes.find(type => type.id === analysisId);
    return analysis ? analysis.name : 'Analyse inconnue';
  }

  getAnalysisPrice(analysisId: number): number {
    const analysis = this.analysisTypes.find(type => type.id === analysisId);
    return analysis ? analysis.price : 0;
  }
}
